'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface JazzLayoutProps {
  children: ReactNode;
  className?: string;
}

export function JazzLayout({ children, className }: JazzLayoutProps) {
  return (
    <div className={cn('h-full w-full flex flex-col', className)}>
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
}
