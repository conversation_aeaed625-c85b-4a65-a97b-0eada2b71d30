'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { JazzHome } from '../pages/JazzHome';
import { JazzCanvas } from '../pages/JazzCanvas';
import { JazzAgentStudio } from '../pages/JazzAgentStudio';
import { JazzKnowledge } from '../pages/JazzKnowledge';

type JazzRoute = 'home' | 'canvas' | 'agent-studio' | 'knowledge';

export function JazzRouter() {
  const searchParams = useSearchParams();
  const [currentRoute, setCurrentRoute] = useState<JazzRoute>('home');
  const [canvasId, setCanvasId] = useState<string | null>(null);

  useEffect(() => {
    const route = searchParams.get('route') as JazzRoute;
    const id = searchParams.get('id');
    
    if (route) {
      setCurrentRoute(route);
    }
    
    if (id) {
      setCanvasId(id);
    }
  }, [searchParams]);

  const renderRoute = () => {
    switch (currentRoute) {
      case 'canvas':
        return <JazzCanvas canvasId={canvasId} />;
      case 'agent-studio':
        return <JazzAgentStudio />;
      case 'knowledge':
        return <JazzKnowledge />;
      default:
        return <JazzHome onNavigate={setCurrentRoute} />;
    }
  };

  return (
    <div className="h-full w-full">
      {renderRoute()}
    </div>
  );
}
