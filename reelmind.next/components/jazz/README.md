# Jazz Integration for Reelmind.next

This directory contains the Jazz React application integrated as a component within the Reelmind.next project.

## Architecture

```
reelmind.next/
├── components/jazz/           # Jazz integration components
│   ├── index.tsx             # Main Jazz app component
│   ├── providers/            # Context providers
│   │   ├── JazzProvider.tsx  # Main provider wrapper
│   │   ├── SocketProvider.tsx # WebSocket connection
│   │   ├── ConfigsProvider.tsx # Configuration management
│   │   └── CanvasProvider.tsx # Canvas state management
│   ├── pages/                # Main page components
│   │   ├── JazzHome.tsx      # Home/dashboard page
│   │   ├── JazzCanvas.tsx    # Canvas page with Excalidraw
│   │   ├── JazzAgentStudio.tsx # Agent studio page
│   │   └── JazzKnowledge.tsx # Knowledge base page
│   ├── components/           # Reusable components
│   │   ├── canvas/           # Canvas-related components
│   │   ├── chat/             # Chat components
│   │   └── ui/               # UI components
│   ├── lib/                  # Utilities and helpers
│   │   ├── api.ts            # API client
│   │   ├── events.ts         # Event bus
│   │   └── utils.ts          # Utility functions
│   └── types/                # TypeScript type definitions
│       └── index.ts          # Main types
```

## Integration Flow

1. **Frontend (reelmind.next)** - Next.js application with Jazz components
2. **Middleware (reelmind.server)** - NestJS backend for auth, credits, and API proxy
3. **Backend (reelmind.py)** - Python FastAPI server with AI capabilities

```
[reelmind.next] → [reelmind.server] → [reelmind.py]
     ↓                    ↓                ↓
  Jazz UI          Auth/Credits      AI Processing
```

## Features

### Canvas
- Excalidraw-based drawing canvas
- Real-time collaboration
- Image generation and manipulation
- Export capabilities

### Chat
- AI-powered chat assistant
- Context-aware responses
- Image analysis
- Tool calling capabilities

### Agent Studio
- Create and manage AI agents
- Workflow automation
- Template system
- Agent deployment

### Knowledge Base
- Document management
- Search capabilities
- Category organization
- File upload and processing

## API Endpoints

All Jazz API calls are proxied through reelmind.server:

- `GET /api/nolan/canvas/:id` - Get canvas data
- `POST /api/nolan/canvas` - Create new canvas
- `PUT /api/nolan/canvas/:id` - Update canvas
- `POST /api/nolan/chat` - Send chat message
- `GET /api/nolan/studio/models` - Get available models
- `POST /api/nolan/studio/generate` - Generate content
- `GET /api/nolan/knowledge` - Get knowledge base
- `POST /api/nolan/knowledge` - Add to knowledge base

## Authentication & Credits

The integration includes:
- User authentication via reelmind.server
- Credit checking before expensive operations
- Automatic credit deduction for AI operations
- Session management

## Usage

### Basic Integration

```tsx
import JazzApp from '@/components/jazz';

export default function NolanPage() {
  return (
    <div className="h-screen w-full">
      <JazzApp className="h-full w-full" />
    </div>
  );
}
```

### Individual Components

```tsx
import { JazzCanvas } from '@/components/jazz/pages/JazzCanvas';
import { JazzProvider } from '@/components/jazz/providers/JazzProvider';

export default function CanvasPage() {
  return (
    <JazzProvider>
      <JazzCanvas canvasId="canvas-123" />
    </JazzProvider>
  );
}
```

## Configuration

Copy `.env.example` to `.env.local` and configure:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_PYTHON_BACKEND_URL=http://localhost:57988
```

## Development

1. Start the Python backend (reelmind.py/server)
2. Start the NestJS middleware (reelmind.server)
3. Start the Next.js frontend (reelmind.next)

The Jazz components will automatically connect to the backend services through the middleware.

## Dependencies

Key dependencies added for Jazz integration:
- `@excalidraw/excalidraw` - Canvas drawing
- `@tanstack/react-query` - Data fetching
- `socket.io-client` - Real-time communication
- `zustand` - State management
- `mitt` - Event bus

## Notes

- The original Jazz project remains unchanged
- All components are adapted to work within the Reelmind ecosystem
- Authentication and credits are handled by reelmind.server
- The Python backend provides AI capabilities without modification
