import mitt from 'mitt';

export interface JazzEvents {
  // Canvas events
  'canvas:created': { canvasId: string };
  'canvas:updated': { canvasId: string; elements: any[] };
  'canvas:deleted': { canvasId: string };
  'canvas:selection-changed': { selectedElements: any[] };
  'canvas:add-images-to-chat': { images: string[]; elements: any[] };

  // Chat events
  'chat:message-sent': { sessionId: string; message: any };
  'chat:message-received': { sessionId: string; message: any };
  'chat:session-created': { sessionId: string };
  'chat:session-deleted': { sessionId: string };
  'chat:typing-start': { sessionId: string };
  'chat:typing-stop': { sessionId: string };

  // Studio events
  'studio:generation-started': { taskId: string; type: string };
  'studio:generation-completed': { taskId: string; result: any };
  'studio:generation-failed': { taskId: string; error: string };
  'studio:template-created': { templateId: string };
  'studio:workflow-executed': { workflowId: string; result: any };

  // Knowledge events
  'knowledge:item-added': { itemId: string };
  'knowledge:item-updated': { itemId: string };
  'knowledge:item-deleted': { itemId: string };
  'knowledge:search-performed': { query: string; results: any[] };

  // UI events
  'ui:theme-changed': { theme: 'light' | 'dark' };
  'ui:sidebar-toggled': { isOpen: boolean };
  'ui:modal-opened': { modalId: string };
  'ui:modal-closed': { modalId: string };
  'ui:notification-shown': { type: 'success' | 'error' | 'warning' | 'info'; message: string };

  // System events
  'system:error': { error: Error; context?: string };
  'system:loading-start': { operation: string };
  'system:loading-end': { operation: string };
  'system:connection-status': { isConnected: boolean };
}

export const eventBus = mitt<JazzEvents>();

// Convenience functions for common events
export const emitCanvasUpdate = (canvasId: string, elements: any[]) => {
  eventBus.emit('canvas:updated', { canvasId, elements });
};

export const emitChatMessage = (sessionId: string, message: any, type: 'sent' | 'received') => {
  eventBus.emit(type === 'sent' ? 'chat:message-sent' : 'chat:message-received', {
    sessionId,
    message,
  });
};

export const emitNotification = (
  type: 'success' | 'error' | 'warning' | 'info',
  message: string
) => {
  eventBus.emit('ui:notification-shown', { type, message });
};

export const emitError = (error: Error, context?: string) => {
  eventBus.emit('system:error', { error, context });
};

export const emitLoadingState = (operation: string, isLoading: boolean) => {
  if (isLoading) {
    eventBus.emit('system:loading-start', { operation });
  } else {
    eventBus.emit('system:loading-end', { operation });
  }
};

// Event listener helpers
export const onCanvasUpdate = (callback: (data: { canvasId: string; elements: any[] }) => void) => {
  eventBus.on('canvas:updated', callback);
  return () => eventBus.off('canvas:updated', callback);
};

export const onChatMessage = (
  callback: (data: { sessionId: string; message: any }) => void,
  type: 'sent' | 'received' | 'both' = 'both'
) => {
  if (type === 'both') {
    eventBus.on('chat:message-sent', callback);
    eventBus.on('chat:message-received', callback);
    return () => {
      eventBus.off('chat:message-sent', callback);
      eventBus.off('chat:message-received', callback);
    };
  } else {
    const eventName = type === 'sent' ? 'chat:message-sent' : 'chat:message-received';
    eventBus.on(eventName, callback);
    return () => eventBus.off(eventName, callback);
  }
};

export const onNotification = (
  callback: (data: { type: 'success' | 'error' | 'warning' | 'info'; message: string }) => void
) => {
  eventBus.on('ui:notification-shown', callback);
  return () => eventBus.off('ui:notification-shown', callback);
};

export const onError = (callback: (data: { error: Error; context?: string }) => void) => {
  eventBus.on('system:error', callback);
  return () => eventBus.off('system:error', callback);
};

export default eventBus;
