import { ApiResponse, PaginatedResponse } from '../types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Canvas API
  async getCanvas(id: string) {
    return this.request(`/nolan/canvas/${id}`);
  }

  async createCanvas(data: any) {
    return this.request('/nolan/canvas', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateCanvas(id: string, data: any) {
    return this.request(`/nolan/canvas/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteCanvas(id: string) {
    return this.request(`/nolan/canvas/${id}`, {
      method: 'DELETE',
    });
  }

  // Chat API
  async sendMessage(data: any) {
    return this.request('/nolan/chat', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getChatHistory(sessionId: string) {
    return this.request(`/nolan/chat/${sessionId}`);
  }

  async cancelChat(sessionId: string) {
    return this.request(`/nolan/chat/cancel/${sessionId}`, {
      method: 'POST',
    });
  }

  // Studio API
  async getModels() {
    return this.request('/nolan/studio/models');
  }

  async generateContent(data: any) {
    return this.request('/nolan/studio/generate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getTemplates() {
    return this.request('/nolan/studio/templates');
  }

  async createTemplate(data: any) {
    return this.request('/nolan/studio/templates', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Knowledge API
  async getKnowledge() {
    return this.request('/nolan/knowledge');
  }

  async addKnowledge(data: any) {
    return this.request('/nolan/knowledge', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async searchKnowledge(query: string) {
    return this.request(`/nolan/knowledge/search?q=${encodeURIComponent(query)}`);
  }

  // Upload API
  async uploadImage(data: any) {
    return this.request('/nolan/upload/image', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async uploadFiles(data: any) {
    return this.request('/nolan/upload/files', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Settings API
  async getSettings() {
    return this.request('/nolan/settings');
  }

  async updateSettings(data: any) {
    return this.request('/nolan/settings', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Config API
  async getConfig() {
    return this.request('/nolan/config');
  }

  async updateConfig(data: any) {
    return this.request('/nolan/config', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }
}

export const apiClient = new ApiClient();

// Convenience functions
export const chatApi = {
  sendMessage: (data: any) => apiClient.sendMessage(data),
  getHistory: (sessionId: string) => apiClient.getChatHistory(sessionId),
  cancel: (sessionId: string) => apiClient.cancelChat(sessionId),
};

export const canvasApi = {
  get: (id: string) => apiClient.getCanvas(id),
  create: (data: any) => apiClient.createCanvas(data),
  update: (id: string, data: any) => apiClient.updateCanvas(id, data),
  delete: (id: string) => apiClient.deleteCanvas(id),
};

export const studioApi = {
  getModels: () => apiClient.getModels(),
  generate: (data: any) => apiClient.generateContent(data),
  getTemplates: () => apiClient.getTemplates(),
  createTemplate: (data: any) => apiClient.createTemplate(data),
};

export const knowledgeApi = {
  get: () => apiClient.getKnowledge(),
  add: (data: any) => apiClient.addKnowledge(data),
  search: (query: string) => apiClient.searchKnowledge(query),
};

export default apiClient;
