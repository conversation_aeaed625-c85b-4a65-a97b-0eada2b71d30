export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: number;
    images?: string[];
  };
}

export interface Session {
  id: string;
  name: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  canvasId?: string;
}

export interface Model {
  id: string;
  name: string;
  provider: string;
  type: 'text' | 'image' | 'multimodal';
  maxTokens?: number;
  supportedFeatures?: string[];
}

export interface Canvas {
  id: string;
  name: string;
  description?: string;
  elements: any[];
  appState: any;
  createdAt: Date;
  updatedAt: Date;
  ownerId: string;
  isPublic: boolean;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  instructions: string;
  model: string;
  tools: string[];
  knowledgeBase?: string[];
  status: 'active' | 'draft' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  status: 'active' | 'draft' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowStep {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'delay';
  config: Record<string, any>;
  nextSteps: string[];
}

export interface WorkflowTrigger {
  id: string;
  type: 'manual' | 'schedule' | 'webhook' | 'canvas_change';
  config: Record<string, any>;
}

export interface KnowledgeItem {
  id: string;
  name: string;
  type: 'document' | 'note' | 'image' | 'url';
  content: string;
  metadata: {
    size?: number;
    mimeType?: string;
    tags?: string[];
    category?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export type PendingType = 'text' | 'image' | 'tool' | null;
