'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createContext, useContext, ReactNode, useState } from 'react';
import { Toaster } from 'sonner';
import { SocketProvider } from './SocketProvider';
import { ConfigsProvider } from './ConfigsProvider';
import { CanvasProvider } from './CanvasProvider';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

interface JazzContextType {
  // Add any global Jazz state here
  isInitialized: boolean;
}

const JazzContext = createContext<JazzContextType | undefined>(undefined);

export function useJazz() {
  const context = useContext(JazzContext);
  if (context === undefined) {
    throw new Error('useJazz must be used within a JazzProvider');
  }
  return context;
}

interface JazzProviderProps {
  children: ReactNode;
}

export function JazzProvider({ children }: JazzProviderProps) {
  const [isInitialized, setIsInitialized] = useState(true);

  const contextValue: JazzContextType = {
    isInitialized,
  };

  return (
    <JazzContext.Provider value={contextValue}>
      <QueryClientProvider client={queryClient}>
        <SocketProvider>
          <ConfigsProvider>
            <CanvasProvider>
              {children}
              <Toaster />
              <ReactQueryDevtools initialIsOpen={false} />
            </CanvasProvider>
          </ConfigsProvider>
        </SocketProvider>
      </QueryClientProvider>
    </JazzContext.Provider>
  );
}
