'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { createContext, useContext, ReactNode, useState } from 'react';
import { ToastProvider } from '../components/ui/toast';
import { SocketProvider } from './SocketProvider';
import { ConfigsProvider } from './ConfigsProvider';
import { CanvasProvider } from './CanvasProvider';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

interface JazzContextType {
  // Add any global Jazz state here
  isInitialized: boolean;
}

const JazzContext = createContext<JazzContextType | undefined>(undefined);

export function useJazz() {
  const context = useContext(JazzContext);
  if (context === undefined) {
    throw new Error('useJazz must be used within a JazzProvider');
  }
  return context;
}

interface JazzProviderProps {
  children: ReactNode;
}

export function JazzProvider({ children }: JazzProviderProps) {
  const [isInitialized, setIsInitialized] = useState(true);

  const contextValue: JazzContextType = {
    isInitialized,
  };

  return (
    <JazzContext.Provider value={contextValue}>
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <SocketProvider>
            <ConfigsProvider>
              <CanvasProvider>
                {children}
                <ReactQueryDevtools initialIsOpen={false} />
              </CanvasProvider>
            </ConfigsProvider>
          </SocketProvider>
        </ToastProvider>
      </QueryClientProvider>
    </JazzContext.Provider>
  );
}
