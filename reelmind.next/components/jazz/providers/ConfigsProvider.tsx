'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';

interface Model {
  id: string;
  name: string;
  provider: string;
  type: 'text' | 'image';
}

interface Config {
  textModel: Model;
  imageModel: Model;
  models: Model[];
  settings: {
    theme: string;
    language: string;
    autoSave: boolean;
  };
}

interface ConfigsContextType {
  config: Config | null;
  updateConfig: (newConfig: Partial<Config>) => void;
  isLoading: boolean;
  error: Error | null;
}

const ConfigsContext = createContext<ConfigsContextType | undefined>(undefined);

export function useConfigs() {
  const context = useContext(ConfigsContext);
  if (context === undefined) {
    throw new Error('useConfigs must be used within a ConfigsProvider');
  }
  return context;
}

interface ConfigsProviderProps {
  children: ReactNode;
}

const defaultConfig: Config = {
  textModel: {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'openai',
    type: 'text',
  },
  imageModel: {
    id: 'dall-e-3',
    name: 'DALL-E 3',
    provider: 'openai',
    type: 'image',
  },
  models: [],
  settings: {
    theme: 'dark',
    language: 'en',
    autoSave: true,
  },
};

export function ConfigsProvider({ children }: ConfigsProviderProps) {
  const [config, setConfig] = useState<Config | null>(defaultConfig);

  // Fetch config from API
  const { data, isLoading, error } = useQuery({
    queryKey: ['jazz-config'],
    queryFn: async () => {
      const response = await fetch('/api/nolan/config');
      if (!response.ok) {
        throw new Error('Failed to fetch config');
      }
      return response.json();
    },
    initialData: defaultConfig,
  });

  useEffect(() => {
    if (data) {
      setConfig(data);
    }
  }, [data]);

  const updateConfig = async (newConfig: Partial<Config>) => {
    try {
      const updatedConfig = { ...config, ...newConfig };
      setConfig(updatedConfig);

      // Update config on server
      await fetch('/api/nolan/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedConfig),
      });
    } catch (error) {
      console.error('Failed to update config:', error);
    }
  };

  const contextValue: ConfigsContextType = {
    config,
    updateConfig,
    isLoading,
    error: error as Error | null,
  };

  return (
    <ConfigsContext.Provider value={contextValue}>
      {children}
    </ConfigsContext.Provider>
  );
}
