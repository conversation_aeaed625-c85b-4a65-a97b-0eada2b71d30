'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import { ExcalidrawImperativeAPI } from '@excalidraw/excalidraw/types/types';

interface CanvasContextType {
  excalidrawAPI: ExcalidrawImperativeAPI | null;
  setExcalidrawAPI: (api: ExcalidrawImperativeAPI) => void;
  canvasId: string | null;
  setCanvasId: (id: string) => void;
}

const CanvasContext = createContext<CanvasContextType | undefined>(undefined);

export function useCanvas() {
  const context = useContext(CanvasContext);
  if (context === undefined) {
    throw new Error('useCanvas must be used within a CanvasProvider');
  }
  return context;
}

interface CanvasProviderProps {
  children: ReactNode;
}

export function CanvasProvider({ children }: CanvasProviderProps) {
  const [excalidrawAPI, setExcalidrawAPI] = useState<ExcalidrawImperativeAPI | null>(null);
  const [canvasId, setCanvasId] = useState<string | null>(null);

  const contextValue: CanvasContextType = {
    excalidrawAPI,
    setExcalidrawAPI,
    canvasId,
    setCanvasId,
  };

  return (
    <CanvasContext.Provider value={contextValue}>
      {children}
    </CanvasContext.Provider>
  );
}
