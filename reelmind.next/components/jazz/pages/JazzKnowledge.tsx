'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { PlusIcon, SearchIcon, FileTextIcon, FolderIcon, UploadIcon } from 'lucide-react';

export function JazzKnowledge() {
  const [searchQuery, setSearchQuery] = useState('');
  const [documents, setDocuments] = useState([
    { id: '1', name: 'Design Guidelines', type: 'document', size: '2.3 MB', updatedAt: new Date() },
    { id: '2', name: 'Brand Assets', type: 'folder', size: '15.7 MB', updatedAt: new Date() },
    { id: '3', name: 'User Research', type: 'document', size: '1.8 MB', updatedAt: new Date() },
  ]);

  const [categories, setCategories] = useState([
    { id: '1', name: 'Design', count: 12 },
    { id: '2', name: 'Development', count: 8 },
    { id: '3', name: 'Research', count: 5 },
    { id: '4', name: 'Brand', count: 3 },
  ]);

  return (
    <div className="h-full w-full p-6 overflow-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Knowledge Base</h1>
          <p className="text-muted-foreground">
            Organize and manage your documents, notes, and knowledge
          </p>
        </div>

        {/* Search and Actions */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search knowledge base..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <UploadIcon className="h-4 w-4 mr-2" />
              Upload
            </Button>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Document
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Categories</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer">
                    <span className="text-sm">{category.name}</span>
                    <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                      {category.count}
                    </span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs defaultValue="documents" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="notes">Notes</TabsTrigger>
                <TabsTrigger value="recent">Recent</TabsTrigger>
              </TabsList>

              <TabsContent value="documents" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {documents.map((doc) => (
                    <Card key={doc.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          {doc.type === 'folder' ? (
                            <FolderIcon className="h-5 w-5 text-blue-500" />
                          ) : (
                            <FileTextIcon className="h-5 w-5 text-gray-500" />
                          )}
                          <CardTitle className="text-base truncate">{doc.name}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <p className="text-sm text-muted-foreground">{doc.size}</p>
                          <p className="text-xs text-muted-foreground">
                            Updated {doc.updatedAt.toLocaleDateString()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="notes" className="space-y-4">
                <div className="text-center py-12">
                  <h3 className="text-lg font-semibold mb-2">No Notes Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first note to get started.
                  </p>
                  <Button>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Note
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="recent" className="space-y-4">
                <div className="text-center py-12">
                  <h3 className="text-lg font-semibold mb-2">No Recent Activity</h3>
                  <p className="text-muted-foreground">
                    Your recently accessed documents will appear here.
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
