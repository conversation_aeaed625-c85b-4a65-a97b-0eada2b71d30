'use client';

import { useState, useEffect } from 'react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { JazzCanvasExcali } from '../components/canvas/JazzCanvasExcali';
import { JazzChat } from '../components/chat/JazzChat';
import { JazzCanvasHeader } from '../components/canvas/JazzCanvasHeader';

interface JazzCanvasProps {
  canvasId: string | null;
}

export function JazzCanvas({ canvasId }: JazzCanvasProps) {
  const [sessions, setSessions] = useState([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  useEffect(() => {
    // Initialize canvas and chat sessions
    if (canvasId) {
      // Load canvas data
      console.log('Loading canvas:', canvasId);
    }
  }, [canvasId]);

  return (
    <div className="h-full w-full flex flex-col">
      {/* Header */}
      <JazzCanvasHeader canvasId={canvasId} />
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Canvas Panel */}
          <ResizablePanel defaultSize={70} minSize={50}>
            <div className="h-full w-full">
              <JazzCanvasExcali canvasId={canvasId} />
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Chat Panel */}
          <ResizablePanel defaultSize={30} minSize={25} maxSize={50}>
            <div className="h-full w-full border-l">
              <JazzChat
                canvasId={canvasId || 'default'}
                sessionList={sessions}
                setSessionList={setSessions}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
