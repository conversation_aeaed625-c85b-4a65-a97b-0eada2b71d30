'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusIcon, CanvasIcon, BotIcon, BookOpenIcon } from 'lucide-react';

interface JazzHomeProps {
  onNavigate: (route: string) => void;
}

export function JazzHome({ onNavigate }: JazzHomeProps) {
  const [canvases, setCanvases] = useState([
    { id: '1', name: 'My First Canvas', description: 'A simple drawing canvas', updatedAt: new Date() },
    { id: '2', name: 'Design Mockup', description: 'UI/UX design mockup', updatedAt: new Date() },
  ]);

  const handleCreateCanvas = () => {
    const newCanvas = {
      id: Date.now().toString(),
      name: 'New Canvas',
      description: 'A new canvas for your ideas',
      updatedAt: new Date(),
    };
    setCanvases([newCanvas, ...canvases]);
    onNavigate('canvas');
  };

  return (
    <div className="h-full w-full p-6 overflow-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Jazz Studio</h1>
          <p className="text-muted-foreground">
            Create, design, and collaborate with AI-powered tools
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleCreateCanvas}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <PlusIcon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">New Canvas</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>Start with a blank canvas</CardDescription>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('agent-studio')}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <BotIcon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Agent Studio</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>Build AI agents and workflows</CardDescription>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('knowledge')}>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <BookOpenIcon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Knowledge Base</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>Manage your knowledge and documents</CardDescription>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <CanvasIcon className="h-5 w-5 text-primary" />
                <CardTitle className="text-lg">Templates</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>Browse canvas templates</CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Recent Canvases */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Recent Canvases</h2>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {canvases.map((canvas) => (
              <Card key={canvas.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onNavigate('canvas')}>
                <CardHeader>
                  <CardTitle className="text-lg">{canvas.name}</CardTitle>
                  <CardDescription>{canvas.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Updated {canvas.updatedAt.toLocaleDateString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
