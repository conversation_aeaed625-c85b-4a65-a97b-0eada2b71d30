'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusIcon, BotIcon, WrenchIcon, PlayIcon } from 'lucide-react';

export function JazzAgentStudio() {
  const [agents, setAgents] = useState([
    { id: '1', name: 'Design Assistant', description: 'Helps with UI/UX design tasks', status: 'active' },
    { id: '2', name: 'Code Generator', description: 'Generates code from designs', status: 'draft' },
  ]);

  const [workflows, setWorkflows] = useState([
    { id: '1', name: 'Design to Code', description: 'Convert designs to React components', steps: 3 },
    { id: '2', name: 'Content Creation', description: 'Generate content for designs', steps: 2 },
  ]);

  return (
    <div className="h-full w-full p-6 overflow-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Agent Studio</h1>
          <p className="text-muted-foreground">
            Build and manage AI agents and automated workflows
          </p>
        </div>

        <Tabs defaultValue="agents" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="agents" className="space-y-6">
            {/* Create Agent Button */}
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Your Agents</h2>
              <Button>
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Agent
              </Button>
            </div>

            {/* Agents Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {agents.map((agent) => (
                <Card key={agent.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <BotIcon className="h-5 w-5 text-primary" />
                        <CardTitle className="text-lg">{agent.name}</CardTitle>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        agent.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {agent.status}
                      </span>
                    </div>
                    <CardDescription>{agent.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        Edit
                      </Button>
                      <Button size="sm">
                        <PlayIcon className="h-3 w-3 mr-1" />
                        Run
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="workflows" className="space-y-6">
            {/* Create Workflow Button */}
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Your Workflows</h2>
              <Button>
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Workflow
              </Button>
            </div>

            {/* Workflows Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {workflows.map((workflow) => (
                <Card key={workflow.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <WrenchIcon className="h-5 w-5 text-primary" />
                      <CardTitle className="text-lg">{workflow.name}</CardTitle>
                    </div>
                    <CardDescription>{workflow.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {workflow.steps} steps
                      </span>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          Edit
                        </Button>
                        <Button size="sm">
                          <PlayIcon className="h-3 w-3 mr-1" />
                          Run
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-semibold mb-2">Templates Coming Soon</h3>
              <p className="text-muted-foreground">
                Pre-built agent and workflow templates will be available here.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
