'use client';

import { useState, useEffect, useCallback } from 'react';
import { Excalidraw, MainMenu, WelcomeScreen } from '@excalidraw/excalidraw';
import { ExcalidrawImperativeAPI } from '@excalidraw/excalidraw/types/types';
import { useCanvas } from '../../providers/CanvasProvider';
import { useTheme } from 'next-themes';

interface JazzCanvasExcaliProps {
  canvasId: string | null;
}

export function JazzCanvasExcali({ canvasId }: JazzCanvasExcaliProps) {
  const { setExcalidrawAPI, setCanvasId } = useCanvas();
  const { theme } = useTheme();
  const [excalidrawAPI, setLocalExcalidrawAPI] = useState<ExcalidrawImperativeAPI | null>(null);

  useEffect(() => {
    if (canvasId) {
      setCanvasId(canvasId);
    }
  }, [canvasId, setCanvasId]);

  const handleAPIReady = useCallback((api: ExcalidrawImperativeAPI) => {
    setLocalExcalidrawAPI(api);
    setExcalidrawAPI(api);
  }, [setExcalidrawAPI]);

  const handleChange = useCallback((elements: any, appState: any) => {
    // Auto-save canvas changes
    if (canvasId && elements.length > 0) {
      // Debounced save to API
      console.log('Canvas changed, saving...', { canvasId, elements: elements.length });
    }
  }, [canvasId]);

  return (
    <div className="h-full w-full">
      <Excalidraw
        excalidrawAPI={handleAPIReady}
        onChange={handleChange}
        theme={theme === 'dark' ? 'dark' : 'light'}
        initialData={{
          elements: [],
          appState: {
            viewBackgroundColor: theme === 'dark' ? '#1a1a1a' : '#ffffff',
          },
        }}
        UIOptions={{
          canvasActions: {
            loadScene: false,
            saveToActiveFile: false,
            export: {
              saveFileToDisk: false,
            },
          },
        }}
      >
        <MainMenu>
          <MainMenu.DefaultItems.ClearCanvas />
          <MainMenu.DefaultItems.SaveAsImage />
          <MainMenu.DefaultItems.ChangeCanvasBackground />
        </MainMenu>
        <WelcomeScreen>
          <WelcomeScreen.Hints.MenuHint />
          <WelcomeScreen.Hints.ToolbarHint />
        </WelcomeScreen>
      </Excalidraw>
    </div>
  );
}
