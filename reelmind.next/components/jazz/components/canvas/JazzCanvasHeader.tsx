'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  ArrowLeftIcon, 
  MoreHorizontalIcon, 
  ShareIcon, 
  DownloadIcon, 
  SettingsIcon,
  SaveIcon
} from 'lucide-react';

interface JazzCanvasHeaderProps {
  canvasId: string | null;
}

export function JazzCanvasHeader({ canvasId }: JazzCanvasHeaderProps) {
  const [canvasName, setCanvasName] = useState('Untitled Canvas');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save canvas to API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      console.log('Canvas saved');
    } catch (error) {
      console.error('Failed to save canvas:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleNameChange = (newName: string) => {
    setCanvasName(newName);
    setIsEditing(false);
    // Auto-save name change
    console.log('Canvas name changed to:', newName);
  };

  return (
    <div className="h-14 border-b bg-background flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm">
          <ArrowLeftIcon className="h-4 w-4" />
        </Button>
        
        <div className="flex items-center gap-2">
          {isEditing ? (
            <Input
              value={canvasName}
              onChange={(e) => setCanvasName(e.target.value)}
              onBlur={() => handleNameChange(canvasName)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleNameChange(canvasName);
                }
                if (e.key === 'Escape') {
                  setIsEditing(false);
                }
              }}
              className="h-8 w-48"
              autoFocus
            />
          ) : (
            <h1 
              className="text-lg font-semibold cursor-pointer hover:bg-muted px-2 py-1 rounded"
              onClick={() => setIsEditing(true)}
            >
              {canvasName}
            </h1>
          )}
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleSave}
          disabled={isSaving}
        >
          <SaveIcon className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save'}
        </Button>

        <Button variant="outline" size="sm">
          <ShareIcon className="h-4 w-4 mr-2" />
          Share
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <DownloadIcon className="h-4 w-4 mr-2" />
              Export
            </DropdownMenuItem>
            <DropdownMenuItem>
              <SettingsIcon className="h-4 w-4 mr-2" />
              Settings
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
