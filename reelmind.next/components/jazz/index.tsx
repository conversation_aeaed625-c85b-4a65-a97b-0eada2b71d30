'use client';

import { Suspense } from 'react';
import { JazzProvider } from './providers/JazzProvider';
import { JazzRouter } from './router/JazzRouter';
import { JazzLayout } from './layout/JazzLayout';

interface JazzAppProps {
  className?: string;
}

export function JazzApp({ className }: JazzAppProps) {
  return (
    <div className={className}>
      <JazzProvider>
        <Suspense fallback={<div>Loading Jazz...</div>}>
          <JazzLayout>
            <JazzRouter />
          </JazzLayout>
        </Suspense>
      </JazzProvider>
    </div>
  );
}

export default JazzApp;
