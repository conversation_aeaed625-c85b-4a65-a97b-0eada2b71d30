'use client';

import { Suspense, useState } from 'react';
import { JazzChat } from '@/components/jazz/components/chat/JazzChat';
import { JazzProvider } from '@/components/jazz/providers/JazzProvider';

export default function NolanChatPage() {
  const [sessions, setSessions] = useState([]);

  return (
    <div className="h-screen w-full">
      <JazzProvider>
        <Suspense fallback={
          <div className="h-full w-full flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading Chat...</p>
            </div>
          </div>
        }>
          <div className="h-full w-full max-w-4xl mx-auto">
            <JazzChat
              canvasId="default"
              sessionList={sessions}
              setSessionList={setSessions}
            />
          </div>
        </Suspense>
      </JazzProvider>
    </div>
  );
}
