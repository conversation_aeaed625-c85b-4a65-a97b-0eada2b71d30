'use client';

import { Suspense } from 'react';
import { JazzKnowledge } from '@/components/jazz/pages/JazzKnowledge';
import { JazzProvider } from '@/components/jazz/providers/JazzProvider';

export default function NolanKnowledgePage() {
  return (
    <div className="h-screen w-full">
      <JazzProvider>
        <Suspense fallback={
          <div className="h-full w-full flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading Knowledge Base...</p>
            </div>
          </div>
        }>
          <JazzKnowledge />
        </Suspense>
      </JazzProvider>
    </div>
  );
}
