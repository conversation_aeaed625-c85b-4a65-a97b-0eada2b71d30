'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { JazzCanvas } from '@/components/jazz/pages/JazzCanvas';
import { JazzProvider } from '@/components/jazz/providers/JazzProvider';

export default function NolanCanvasPage() {
  const searchParams = useSearchParams();
  const canvasId = searchParams.get('id');

  return (
    <div className="h-screen w-full">
      <JazzProvider>
        <Suspense fallback={
          <div className="h-full w-full flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading Canvas...</p>
            </div>
          </div>
        }>
          <JazzCanvas canvasId={canvasId} />
        </Suspense>
      </JazzProvider>
    </div>
  );
}
