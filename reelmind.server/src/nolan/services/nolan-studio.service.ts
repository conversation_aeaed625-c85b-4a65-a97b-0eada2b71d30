import { Injectable } from '@nestjs/common';
import { NolanPythonService } from './nolan-python.service';

@Injectable()
export class NolanStudioService {
  constructor(private readonly pythonService: NolanPythonService) {}

  /**
   * Get available models from Python backend
   */
  async getModels(user: any): Promise<any> {
    return this.pythonService.getModels(user);
  }

  /**
   * Generate content using Python backend
   */
  async generateContent(generateDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/studio/generate', generateDto, user);
  }

  /**
   * Get generation history
   */
  async getGenerationHistory(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/studio/history', null, user);
  }

  /**
   * Save generated content
   */
  async saveGeneration(generationDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/studio/save', generationDto, user);
  }

  /**
   * Delete generation
   */
  async deleteGeneration(generationId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/studio/generation/${generationId}`, null, user);
  }

  /**
   * Get templates
   */
  async getTemplates(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/studio/templates', null, user);
  }

  /**
   * Create template
   */
  async createTemplate(templateDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/studio/templates', templateDto, user);
  }

  /**
   * Update template
   */
  async updateTemplate(templateId: string, templateDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/studio/templates/${templateId}`, templateDto, user);
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/studio/templates/${templateId}`, null, user);
  }

  /**
   * Get workflows
   */
  async getWorkflows(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/studio/workflows', null, user);
  }

  /**
   * Create workflow
   */
  async createWorkflow(workflowDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/studio/workflows', workflowDto, user);
  }

  /**
   * Execute workflow
   */
  async executeWorkflow(workflowId: string, inputDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', `/api/studio/workflows/${workflowId}/execute`, inputDto, user);
  }
}
