import { Injectable } from '@nestjs/common';
import { NolanPythonService } from './nolan-python.service';

@Injectable()
export class NolanKnowledgeService {
  constructor(private readonly pythonService: NolanPythonService) {}

  /**
   * Get knowledge base from Python backend
   */
  async getKnowledge(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/knowledge', null, user);
  }

  /**
   * Add to knowledge base
   */
  async addKnowledge(knowledgeDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/knowledge', knowledgeDto, user);
  }

  /**
   * Update knowledge item
   */
  async updateKnowledge(knowledgeId: string, knowledgeDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/knowledge/${knowledgeId}`, knowledgeDto, user);
  }

  /**
   * Delete knowledge item
   */
  async deleteKnowledge(knowledgeId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/knowledge/${knowledgeId}`, null, user);
  }

  /**
   * Search knowledge base
   */
  async searchKnowledge(query: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', `/api/knowledge/search?q=${encodeURIComponent(query)}`, null, user);
  }

  /**
   * Get knowledge categories
   */
  async getCategories(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/knowledge/categories', null, user);
  }

  /**
   * Create knowledge category
   */
  async createCategory(categoryDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/knowledge/categories', categoryDto, user);
  }

  /**
   * Update knowledge category
   */
  async updateCategory(categoryId: string, categoryDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/knowledge/categories/${categoryId}`, categoryDto, user);
  }

  /**
   * Delete knowledge category
   */
  async deleteCategory(categoryId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/knowledge/categories/${categoryId}`, null, user);
  }

  /**
   * Import knowledge from file
   */
  async importKnowledge(importDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/knowledge/import', importDto, user);
  }

  /**
   * Export knowledge to file
   */
  async exportKnowledge(format: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', `/api/knowledge/export?format=${format}`, null, user);
  }
}
