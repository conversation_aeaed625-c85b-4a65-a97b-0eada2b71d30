import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NolanPythonService {
  private readonly pythonBackendUrl: string;

  constructor(
    private readonly configService: ConfigService,
  ) {
    this.pythonBackendUrl = this.configService.get<string>('PYTHON_BACKEND_URL', 'http://localhost:57988');
  }

  /**
   * Make authenticated request to Python backend
   */
  private async makeRequest(method: string, endpoint: string, data?: any, user?: any): Promise<any> {
    try {
      const url = `${this.pythonBackendUrl}${endpoint}`;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        // Add user authentication headers if needed
        ...(user && { 'X-User-ID': user.id }),
      };

      const config: RequestInit = {
        method: method.toUpperCase(),
        headers,
      };

      if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        config.body = JSON.stringify(data);
      }

      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();
      return responseData;
    } catch (error) {
      console.error(`Error making request to Python backend:`, error);
      throw new HttpException(
        `Python backend error: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Upload image to Python backend
   */
  async uploadImage(uploadDto: any, user: any): Promise<any> {
    return this.makeRequest('POST', '/api/upload_image', uploadDto, user);
  }

  /**
   * Upload files to Python backend
   */
  async uploadFiles(uploadDto: any, user: any): Promise<any> {
    return this.makeRequest('POST', '/api/upload_files', uploadDto, user);
  }

  /**
   * Get configuration from Python backend
   */
  async getConfig(user: any): Promise<any> {
    return this.makeRequest('GET', '/api/config', null, user);
  }

  /**
   * Update configuration in Python backend
   */
  async updateConfig(configDto: any, user: any): Promise<any> {
    return this.makeRequest('PUT', '/api/config', configDto, user);
  }

  /**
   * Get models from Python backend
   */
  async getModels(user: any): Promise<any> {
    return this.makeRequest('GET', '/api/model', null, user);
  }

  /**
   * Update models in Python backend
   */
  async updateModels(modelsDto: any, user: any): Promise<any> {
    return this.makeRequest('PUT', '/api/model', modelsDto, user);
  }

  /**
   * Get settings from Python backend
   */
  async getSettings(user: any): Promise<any> {
    return this.makeRequest('GET', '/api/settings', null, user);
  }

  /**
   * Update settings in Python backend
   */
  async updateSettings(settingsDto: any, user: any): Promise<any> {
    return this.makeRequest('PUT', '/api/settings', settingsDto, user);
  }

  /**
   * Health check for Python backend
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.makeRequest('GET', '/health');
      return true;
    } catch (error) {
      console.error('Python backend health check failed:', error);
      return false;
    }
  }
}
