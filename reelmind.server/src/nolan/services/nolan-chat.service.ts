import { Injectable } from '@nestjs/common';
import { NolanPythonService } from './nolan-python.service';

@Injectable()
export class NolanChatService {
  constructor(private readonly pythonService: NolanPythonService) {}

  /**
   * Send chat message to Python backend
   */
  async sendMessage(chatDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/chat', chatDto, user);
  }

  /**
   * Get chat history from Python backend
   */
  async getChatHistory(sessionId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', `/api/chat/${sessionId}`, null, user);
  }

  /**
   * Cancel chat session in Python backend
   */
  async cancelSession(sessionId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', `/api/chat/${sessionId}/cancel`, null, user);
  }

  /**
   * Create new chat session
   */
  async createSession(sessionDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/chat/session', sessionDto, user);
  }

  /**
   * Delete chat session
   */
  async deleteSession(sessionId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/chat/${sessionId}`, null, user);
  }

  /**
   * Get all chat sessions for user
   */
  async getUserSessions(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/chat/sessions', null, user);
  }

  /**
   * Update chat session settings
   */
  async updateSessionSettings(sessionId: string, settingsDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/chat/${sessionId}/settings`, settingsDto, user);
  }

  /**
   * Export chat session
   */
  async exportSession(sessionId: string, format: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', `/api/chat/${sessionId}/export?format=${format}`, null, user);
  }
}
