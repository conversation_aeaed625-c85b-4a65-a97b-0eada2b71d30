import { Injectable } from '@nestjs/common';
import { NolanPythonService } from './nolan-python.service';

@Injectable()
export class NolanCanvasService {
  constructor(private readonly pythonService: NolanPythonService) {}

  /**
   * Get canvas by ID from Python backend
   */
  async getCanvas(canvasId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', `/api/canvas/${canvasId}`, null, user);
  }

  /**
   * Create new canvas in Python backend
   */
  async createCanvas(createCanvasDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', '/api/canvas', createCanvasDto, user);
  }

  /**
   * Update canvas in Python backend
   */
  async updateCanvas(canvasId: string, updateCanvasDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/canvas/${canvasId}`, updateCanvasDto, user);
  }

  /**
   * Delete canvas from Python backend
   */
  async deleteCanvas(canvasId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/canvas/${canvasId}`, null, user);
  }

  /**
   * Get all canvases for user from Python backend
   */
  async getUserCanvases(user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('GET', '/api/canvas', null, user);
  }

  /**
   * Add node to canvas
   */
  async addNode(canvasId: string, nodeDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', `/api/canvas/${canvasId}/nodes`, nodeDto, user);
  }

  /**
   * Update node in canvas
   */
  async updateNode(canvasId: string, nodeId: string, nodeDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('PUT', `/api/canvas/${canvasId}/nodes/${nodeId}`, nodeDto, user);
  }

  /**
   * Delete node from canvas
   */
  async deleteNode(canvasId: string, nodeId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/canvas/${canvasId}/nodes/${nodeId}`, null, user);
  }

  /**
   * Add edge to canvas
   */
  async addEdge(canvasId: string, edgeDto: any, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('POST', `/api/canvas/${canvasId}/edges`, edgeDto, user);
  }

  /**
   * Delete edge from canvas
   */
  async deleteEdge(canvasId: string, edgeId: string, user: any): Promise<any> {
    return (this.pythonService as any).makeRequest('DELETE', `/api/canvas/${canvasId}/edges/${edgeId}`, null, user);
  }
}
