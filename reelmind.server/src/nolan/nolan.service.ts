import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreditsService } from '../credits/credits.service';
import { UserService } from '../user/user.service';
import { NolanPythonService } from './services/nolan-python.service';

@Injectable()
export class NolanService {
  private readonly pythonBackendUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly creditsService: CreditsService,
    private readonly userService: UserService,
    private readonly pythonService: NolanPythonService,
  ) {
    this.pythonBackendUrl = this.configService.get<string>('PYTHON_BACKEND_URL', 'http://localhost:57988');
  }

  /**
   * Check if user has sufficient credits for nolan operations
   */
  async checkUserCredits(userId: string): Promise<boolean> {
    try {
      const balance = await this.creditsService.getUserBalance(userId);
      return balance > 0;
    } catch (error) {
      console.error('Error checking user credits:', error);
      return false;
    }
  }

  /**
   * Deduct credits for nolan operations
   */
  async deductCredits(userId: string, amount: number, description: string): Promise<void> {
    try {
      await this.creditsService.deductCredits(userId, amount, description);
    } catch (error) {
      console.error('Error deducting credits:', error);
      throw new HttpException('Failed to deduct credits', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Upload image to Python backend
   */
  async uploadImage(uploadDto: any, user: any): Promise<any> {
    return this.pythonService.uploadImage(uploadDto, user);
  }

  /**
   * Upload files to Python backend
   */
  async uploadFiles(uploadDto: any, user: any): Promise<any> {
    return this.pythonService.uploadFiles(uploadDto, user);
  }

  /**
   * Get user settings for nolan
   */
  async getUserSettings(userId: string): Promise<any> {
    try {
      // Get user settings from database or return defaults
      const settings = {
        theme: 'dark',
        language: 'en',
        autoSave: true,
        notifications: true,
        // Add more default settings as needed
      };
      return settings;
    } catch (error) {
      console.error('Error getting user settings:', error);
      throw new HttpException('Failed to get user settings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user settings for nolan
   */
  async updateUserSettings(userId: string, settingsDto: any): Promise<any> {
    try {
      // Update user settings in database
      // For now, just return the updated settings
      return settingsDto;
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw new HttpException('Failed to update user settings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get configuration for nolan
   */
  async getConfig(user: any): Promise<any> {
    return this.pythonService.getConfig(user);
  }

  /**
   * Update configuration for nolan
   */
  async updateConfig(configDto: any, user: any): Promise<any> {
    return this.pythonService.updateConfig(configDto, user);
  }

  /**
   * Validate user authentication and permissions
   */
  async validateUserAccess(userId: string): Promise<boolean> {
    try {
      const user = await this.userService.findById(userId);
      return !!user;
    } catch (error) {
      console.error('Error validating user access:', error);
      return false;
    }
  }

  /**
   * Log nolan activity for analytics
   */
  async logActivity(userId: string, action: string, metadata?: any): Promise<void> {
    try {
      // Log activity to database or analytics service
      console.log(`Nolan activity - User: ${userId}, Action: ${action}`, metadata);
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }
}
