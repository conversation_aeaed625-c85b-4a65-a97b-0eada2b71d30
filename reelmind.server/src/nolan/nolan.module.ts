import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>roll<PERSON> } from './nolan.controller';
import { NolanService } from './nolan.service';
import { NolanCanvasService } from './services/nolan-canvas.service';
import { NolanChatService } from './services/nolan-chat.service';
import { NolanStudioService } from './services/nolan-studio.service';
import { NolanKnowledgeService } from './services/nolan-knowledge.service';
import { Nolan<PERSON><PERSON>honService } from './services/nolan-python.service';
import { CreditsModule } from '../credits/credits.module';
import { UserModule } from '../user/user.module';

@Module({
  imports: [CreditsModule, UserModule],
  controllers: [NolanController],
  providers: [
    NolanService,
    NolanCanvasService,
    NolanChatService,
    NolanStudioService,
    NolanKnowledgeService,
    NolanPythonService,
  ],
  exports: [NolanService],
})
export class NolanModule {}
