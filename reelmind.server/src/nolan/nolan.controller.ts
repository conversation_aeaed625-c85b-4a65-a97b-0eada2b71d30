import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NolanService } from './nolan.service';
import { NolanCanvasService } from './services/nolan-canvas.service';
import { NolanChatService } from './services/nolan-chat.service';
import { NolanStudioService } from './services/nolan-studio.service';
import { NolanKnowledgeService } from './services/nolan-knowledge.service';
import { JwtGuard } from '../common/guards/jwt.guard';

@ApiTags('nolan')
@Controller('api/nolan')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class NolanController {
  constructor(
    private readonly nolanService: NolanService,
    private readonly canvasService: NolanCanvasService,
    private readonly chatService: NolanChatService,
    private readonly studioService: NolanStudioService,
    private readonly knowledgeService: NolanKnowledgeService,
  ) {}

  // Canvas endpoints - proxy to Python backend
  @Get('canvas/:id')
  @ApiOperation({ summary: 'Get canvas by ID' })
  async getCanvas(@Param('id') id: string, @Request() req: any) {
    return this.canvasService.getCanvas(id, req.user);
  }

  @Post('canvas')
  @ApiOperation({ summary: 'Create new canvas' })
  async createCanvas(@Body() createCanvasDto: any, @Request() req: any) {
    return this.canvasService.createCanvas(createCanvasDto, req.user);
  }

  @Put('canvas/:id')
  @ApiOperation({ summary: 'Update canvas' })
  async updateCanvas(
    @Param('id') id: string,
    @Body() updateCanvasDto: any,
    @Request() req: any,
  ) {
    return this.canvasService.updateCanvas(id, updateCanvasDto, req.user);
  }

  // Chat endpoints - proxy to Python backend
  @Post('chat')
  @ApiOperation({ summary: 'Send chat message' })
  async sendChatMessage(@Body() chatDto: any, @Request() req: any) {
    // Check credits before processing
    const hasCredits = await this.nolanService.checkUserCredits(req.user.id);
    if (!hasCredits) {
      throw new HttpException('Insufficient credits', HttpStatus.PAYMENT_REQUIRED);
    }

    return this.chatService.sendMessage(chatDto, req.user);
  }

  @Post('chat/cancel/:sessionId')
  @ApiOperation({ summary: 'Cancel chat session' })
  async cancelChat(@Param('sessionId') sessionId: string, @Request() req: any) {
    return this.chatService.cancelSession(sessionId, req.user);
  }

  // Studio endpoints - proxy to Python backend
  @Get('studio/models')
  @ApiOperation({ summary: 'Get available models' })
  async getModels(@Request() req: any) {
    return this.studioService.getModels(req.user);
  }

  @Post('studio/generate')
  @ApiOperation({ summary: 'Generate content' })
  async generateContent(@Body() generateDto: any, @Request() req: any) {
    // Check credits before processing
    const hasCredits = await this.nolanService.checkUserCredits(req.user.id);
    if (!hasCredits) {
      throw new HttpException('Insufficient credits', HttpStatus.PAYMENT_REQUIRED);
    }

    return this.studioService.generateContent(generateDto, req.user);
  }

  // Knowledge endpoints - proxy to Python backend
  @Get('knowledge')
  @ApiOperation({ summary: 'Get knowledge base' })
  async getKnowledge(@Request() req: any) {
    return this.knowledgeService.getKnowledge(req.user);
  }

  @Post('knowledge')
  @ApiOperation({ summary: 'Add to knowledge base' })
  async addKnowledge(@Body() knowledgeDto: any, @Request() req: any) {
    return this.knowledgeService.addKnowledge(knowledgeDto, req.user);
  }

  // File upload endpoints
  @Post('upload/image')
  @ApiOperation({ summary: 'Upload image' })
  async uploadImage(@Body() uploadDto: any, @Request() req: any) {
    return this.nolanService.uploadImage(uploadDto, req.user);
  }

  @Post('upload/files')
  @ApiOperation({ summary: 'Upload files' })
  async uploadFiles(@Body() uploadDto: any, @Request() req: any) {
    return this.nolanService.uploadFiles(uploadDto, req.user);
  }

  // Settings endpoints
  @Get('settings')
  @ApiOperation({ summary: 'Get user settings' })
  async getSettings(@Request() req: any) {
    return this.nolanService.getUserSettings(req.user.id);
  }

  @Put('settings')
  @ApiOperation({ summary: 'Update user settings' })
  async updateSettings(@Body() settingsDto: any, @Request() req: any) {
    return this.nolanService.updateUserSettings(req.user.id, settingsDto);
  }

  // Config endpoints
  @Get('config')
  @ApiOperation({ summary: 'Get configuration' })
  async getConfig(@Request() req: any) {
    return this.nolanService.getConfig(req.user);
  }

  @Put('config')
  @ApiOperation({ summary: 'Update configuration' })
  async updateConfig(@Body() configDto: any, @Request() req: any) {
    return this.nolanService.updateConfig(configDto, req.user);
  }
}
