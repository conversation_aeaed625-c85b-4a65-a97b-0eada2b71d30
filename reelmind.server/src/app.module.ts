import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerMiddleware } from './common/middleware/logger.middleware';
import { LoggerModule } from './common/services/logger.module';
import { EnvValidatorService } from './common/services/env-validator.service';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { UserModule } from './user/user.module';
import { SupabaseModule } from './common/providers/supabase.module';
import { ValidationExceptionFilter } from './common/filters/validation-exception.filter';
import { GenerationModule } from './generation/generation.module';
import { PaymentModule } from './payment/payment.module';
import { MembershipModule } from './membership/membership.module';
import { PostModule } from './post/post.module';
import { CreditsModule } from './credits/credits.module';
import { TagsModule } from './tags/tags.module';
import { SentryModule } from '@sentry/nestjs/setup';
import { AdminModule } from './common/module/admin.module';
import { VideoModule } from './video/video.module';
import { ModelsModule } from './models/models.module';
import { SoundModule } from './sound/sound.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { TrainModule } from './train/train.module';
import { LegoModule } from './lego/lego.module';
import { BlogModule } from './blog/blog.module';
import { EmailValidatorModule } from './common/module/email-validator.module';
import { CouponModule } from './coupon/coupon.module';
import { FeedbackModule } from './feedback/feedback.module';
import { NolanModule } from './nolan/nolan.module';

@Module({
  imports: [
    SentryModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule,
    SupabaseModule,
    EmailValidatorModule,
    UserModule,
    MembershipModule,
    GenerationModule,
    PaymentModule,
    PostModule,
    CreditsModule,
    TagsModule,
    AdminModule,
    VideoModule,
    ModelsModule,
    SchedulerModule,
    TrainModule,
    LegoModule,
    BlogModule,
    CouponModule,
    FeedbackModule,
    SoundModule,
    NolanModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    EnvValidatorService, // 添加环境变量验证服务
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: ValidationExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  constructor(private readonly envValidator: EnvValidatorService) {
    // 启动时验证环境变量
    const validation = this.envValidator.validateEnvironment();
    if (!validation.isValid) {
      console.error('Environment validation failed:', validation.errors);
      process.exit(1); // 环境配置错误时退出应用
    }

    if (validation.warnings.length > 0) {
      console.warn('Environment validation warnings:', validation.warnings);
    }
  }

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(
        LoggerMiddleware,
      )
      .forRoutes('*');
  }
}
