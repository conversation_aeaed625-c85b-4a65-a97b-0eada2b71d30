/**
 * 积分系统常量定义
 *
 * 本文件定义了积分系统中使用的所有常量，包括交易类型、积分数量、状态等
 * 遵循单一职责原则，确保常量定义清晰且易于维护
 */

/**
 * 积分交易类型枚举
 * 用于标识不同类型的积分交易
 */
export enum CreditTransactionType {
    // 获取积分类型
    MEMBERSHIP_MONTHLY = 'membership_monthly',   // 会员每月赠送
    MEMBERSHIP_INITIAL = 'membership_initial',   // 会员首次开通赠送
    FREE_USER_MONTHLY = 'free_user_monthly',     // 免费用户每月赠送
    DIRECT_PURCHASE = 'direct_purchase',         // 直接购买
    PLATFORM_REWARD = 'platform_reward',         // 平台奖励
    NEW_USER_BONUS = 'new_user_bonus',           // 新用户注册奖励
    POST_REWARD = 'post_reward',                 // 发帖奖励
    REFUND = 'refund',                           // 退款
    BALANCE_ADJUSTMENT = 'balance_adjustment',   // 余额调整（管理员操作）

    // 消费积分类型
    VIDEO_GENERATION = 'video_generation',       // 视频生成
    PIC_GENERATION = 'pic_generation',           // 图片生成
    MODEL_TRAINING = 'model_training',           // 模型训练
    SOUND_GENERATION = 'sound_generation',       // 声音生成
    NOLAN_OPERATION = 'nolan_operation',         // Nolan AI 操作
}

/**
 * 积分数量配置
 * 定义了各种操作的默认积分数量
 */
export enum CreditAmount {
    // 获取积分数量
    FREE_USER_MONTHLY = 80,       // 免费用户每月赠送
    PRO_MONTHLY = 1288,           // PRO会员每月赠送
    MAX_MONTHLY = 2888,           // MAX会员每月赠送
    NEW_USER_BONUS = 88,          // 新用户注册奖励
    POST_REWARD = 50,             // 发帖奖励

    // 消费积分数量
    VIDEO_GENERATION_SD = 50,     // 标准视频生成
    VIDEO_GENERATION_HD = 80,     // 高清视频生成
    VIDEO_TRAIN = 2000,            // 模型训练

    // 图片生成积分
    PIC_GENERATION_LOW = 2,       // 低分辨率图片生成
    PIC_GENERATION_MEDIUM = 5,    // 中等分辨率图片生成
    PIC_GENERATION_HIGH = 10,     // 高分辨率图片生成

    // 声音生成积分
    SOUND_GENERATION_SHORT = 1,   // 短文本声音生成
    SOUND_GENERATION_MEDIUM = 2,  // 中等文本声音生成
    SOUND_GENERATION_LONG = 3,    // 长文本声音生成
    SOUND_EFFECT = 2,             // 音效生成
}

/**
 * 积分交易状态枚举
 * 用于标识积分交易的当前状态
 */
export enum CreditTransactionStatus {
    PENDING = 'pending',          // 待处理
    COMPLETED = 'completed',      // 已完成
    FAILED = 'failed',            // 失败
    REFUNDED = 'refunded',        // 已退款
}

/**
 * 积分系统配置常量
 */
export const CREDITS_CONFIG = {
    // 积分兑换率
    EXCHANGE_RATE: 100,           // 1 USD = 100 Credits

    // 每日限制
    DAILY_POST_REWARD_LIMIT: 1,   // 每日发帖奖励次数限制

    // 新用户奖励
    NEW_USER_BONUS_AMOUNT: 88,    // 新用户注册奖励积分

    // 积分刷新配置
    MONTHLY_REFRESH: {
        ENABLED: true,            // 是否启用每月刷新
        DAY_OF_MONTH: 1,          // 每月刷新的日期（1-28）
        BATCH_SIZE: 100,          // 每批处理的用户数
        RETRY_DELAY: 1000,        // 重试延迟（毫秒）
        MAX_RETRIES: 3,           // 最大重试次数
    },

    // 缓存配置
    CACHE: {
        TTL: 300,                 // 缓存有效期（秒）
        REFRESH_INTERVAL: 60,     // 缓存刷新间隔（秒）
    },

    // 锁定配置
    LOCK: {
        TIMEOUT: 30 * 60,         // 锁定超时时间（秒）
        KEY_PREFIX: 'credits_lock_', // 锁定键前缀
    }
};

// 为了向后兼容，保留这些常量
export const CREDIT_EXCHANGE_RATE = CREDITS_CONFIG.EXCHANGE_RATE;
export const DAILY_POST_REWARD_LIMIT = CREDITS_CONFIG.DAILY_POST_REWARD_LIMIT;
export const NEW_USER_BONUS_AMOUNT = CREDITS_CONFIG.NEW_USER_BONUS_AMOUNT;
